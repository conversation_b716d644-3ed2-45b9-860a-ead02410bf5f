import { useState, useEffect, useCallback, useRef } from 'react';
import { SpeechRecognitionService } from '../services/SpeechRecognitionService';
import { AppService } from '../services/AppService';
import { ConversationStorage, type StoredMessage } from '../services/ConversationStorage';
import type { ConversationState, SpeechRecognitionResult } from '../services/impl/ISpeechRecognitionService';

export interface ConversationMessage {
  id: string;
  type: 'user' | 'ai';
  content: string;
  timestamp: Date;
  isInterim?: boolean;
}

export interface UseRealTimeConversationReturn {
  // Estado
  isActive: boolean;
  conversationState: ConversationState;
  messages: ConversationMessage[];
  currentUserInput: string;
  isSupported: boolean;
  error: string | null;

  // Controles
  startConversation: () => Promise<boolean>;
  stopConversation: () => void;
  sendMessage: (message: string) => Promise<void>;
  clearMessages: () => void;
  addInitialMessage: (content: string) => void; // 👈 Nuevo método

  // Configuración
  enableSmartMicrophone: () => void;
  disableSmartMicrophone: () => void;
}

// Convertir StoredMessage a ConversationMessage
const convertStoredToConversation = (stored: StoredMessage): ConversationMessage => ({
  id: stored.id,
  type: stored.role === 'user' ? 'user' : 'ai',
  content: stored.content,
  timestamp: new Date(stored.createdAt),
  isInterim: false
});

export const useRealTimeConversation = (
  generatedCharacter?: string,
  isGameStarted?: boolean
): UseRealTimeConversationReturn => {
  const [isActive, setIsActive] = useState(false);
  const [conversationState, setConversationState] = useState<ConversationState>('idle');
  const [messages, setMessages] = useState<ConversationMessage[]>([]);
  const [currentUserInput, setCurrentUserInput] = useState('');
  const [error, setError] = useState<string | null>(null);

  const speechService = useRef(SpeechRecognitionService.getInstance());
  const appService = useRef(AppService.getInstance());
  const storage = useRef(ConversationStorage.getInstance());
  const interimMessageId = useRef<string | null>(null);

  const isSupported = speechService.current.isSupported();

  // Cargar mensajes desde localStorage al inicializar
  useEffect(() => {
    const loadStoredMessages = () => {
      const session = storage.current.loadLatestSession();
      if (session && session.messages.length > 0) {
        const convertedMessages = session.messages.map(convertStoredToConversation);
        setMessages(convertedMessages);
        console.log('📖 Mensajes cargados desde localStorage:', convertedMessages.length);
      }
    };

    loadStoredMessages();
  }, []);

  // Crear nueva sesión cuando cambia el personaje generado
  useEffect(() => {
    if (generatedCharacter) {
      storage.current.createNewSession(generatedCharacter);
      console.log('🆕 Nueva sesión creada para personaje:', generatedCharacter);
    }
  }, [generatedCharacter]);



  // Agregar mensaje inicial de la IA (seed message)
  const addInitialMessage = useCallback((content: string) => {
    try {
      const seedMessage = storage.current.addSeedMessage(content);
      const convertedMessage = convertStoredToConversation(seedMessage);

      // Verificar si ya existe este mensaje en el estado
      setMessages(prev => {
        const existingSeed = prev.find(m => m.id === 'seed-1');
        if (existingSeed) {
          return prev; // Ya existe, no agregar duplicado
        }
        return [convertedMessage, ...prev]; // Agregar al inicio
      });

      console.log('🌱 Mensaje inicial agregado al storage y estado');
    } catch (error) {
      console.error('❌ Error agregando mensaje inicial:', error);
    }
  }, []);

  // Agregar mensaje regular al historial
  const addMessage = useCallback((type: 'user' | 'ai', content: string, isInterim = false) => {
    if (!content.trim()) return '';

    const timestamp = new Date();
    const messageId = `${type}_${timestamp.getTime()}`;

    const newMessage: ConversationMessage = {
      id: messageId,
      type,
      content,
      timestamp,
      isInterim
    };

    if (isInterim) {
      // Mensaje temporal - solo en estado, no en storage
      interimMessageId.current = messageId;
      setMessages(prev => {
        const filtered = prev.filter(m => !m.isInterim || m.type !== type);
        return [...filtered, newMessage];
      });
    } else {
      // Mensaje final - agregar a storage y estado
      try {
        const role = type === 'user' ? 'user' : 'assistant';
        const storedMessage = storage.current.addMessage(role, content);
        const convertedMessage = convertStoredToConversation(storedMessage);

        setMessages(prev => {
          // Remover mensajes interim del mismo tipo
          const filtered = prev.filter(m => !m.isInterim || m.type !== type);
          return [...filtered, convertedMessage];
        });

        interimMessageId.current = null;
      } catch (error) {
        console.error('❌ Error guardando mensaje:', error);
        // Fallback: agregar solo al estado
        setMessages(prev => {
          const filtered = prev.filter(m => !m.isInterim || m.type !== type);
          return [...filtered, newMessage];
        });
      }
    }

    return messageId;
  }, []);

  // Finalizar mensaje interim
  const finalizeMessage = useCallback((content: string) => {
    if (interimMessageId.current) {
      // Reemplazar el mensaje interim con uno final
      addMessage('user', content, false);
    }
  }, [addMessage]);

  // Enviar mensaje a la IA
  const sendMessage = useCallback(async (message: string) => {
    if (!message.trim()) return;

    try {
      setConversationState('processing');
      speechService.current.setConversationState('processing');

      // Incluir información del personaje en el contexto si está disponible
      const queryWithContext = generatedCharacter
        ? `Personaje a adivinar: ${generatedCharacter}\n\nRespuesta del jugador: ${message}`
        : message;

      const response = await appService.current.generateWithIaVsPlayer(queryWithContext);
      const responseText = response.response || response.output || response.result || response.text || response.content || 'Respuesta no encontrada';

      // Agregar respuesta de la IA al historial
      addMessage('ai', responseText);

      // Cambiar estado a "hablando"
      setConversationState('speaking');
      speechService.current.setConversationState('speaking');

    } catch (error) {
      console.error('❌ Error procesando mensaje:', error);
      setError('Error al procesar el mensaje');
      setConversationState('idle');
      speechService.current.setConversationState('idle');
    }
  }, [generatedCharacter, addMessage]);

  // Manejar resultados del reconocimiento de voz
  const handleSpeechResult = useCallback((result: SpeechRecognitionResult) => {
    const { transcript, isFinal } = result;

    if (transcript.trim()) {
      if (isFinal) {
        // Finalizar el mensaje y enviarlo
        finalizeMessage(transcript);
        setCurrentUserInput('');
        sendMessage(transcript);
      } else {
        // Mostrar resultado interim
        addMessage('user', transcript, true);
        setCurrentUserInput(transcript);
      }
    }
  }, [finalizeMessage, sendMessage, addMessage]);

  // Manejar cambios de estado
  const handleStateChange = useCallback((change: any) => {
    setConversationState(change.state);
  }, []);

  // Manejar errores
  const handleError = useCallback((errorMessage: string) => {
    setError(errorMessage);
    setConversationState('idle');
  }, []);

  // Manejar cuando el audio termine
  const handleAudioFinished = useCallback(() => {
    if (isActive) {
      setConversationState('idle');
      speechService.current.setConversationState('idle');
    }
  }, [isActive]);

  // Inicializar callbacks del servicio de voz
  useEffect(() => {
    speechService.current.onResult(handleSpeechResult);
    speechService.current.onStateChange(handleStateChange);
    speechService.current.onError(handleError);
    appService.current.setAudioFinishedCallback(handleAudioFinished);
  }, [handleSpeechResult, handleStateChange, handleError, handleAudioFinished]);

  // Iniciar conversación
  const startConversation = useCallback(async (): Promise<boolean> => {
    if (!isSupported) {
      const errorMsg = 'El reconocimiento de voz no está soportado en este navegador';
      setError(errorMsg);
      return false;
    }

    setError(null);
    setIsActive(true);

    const success = await speechService.current.startListening();
    if (!success) {
      setIsActive(false);
      const errorMsg = 'No se pudo iniciar el reconocimiento de voz';
      setError(errorMsg);
      return false;
    }

    return true;
  }, [isSupported]);

  // Detener conversación
  const stopConversation = useCallback(() => {
    speechService.current.stopListening();
    setIsActive(false);
    setConversationState('idle');
    setCurrentUserInput('');
  }, []);

  // Limpiar mensajes
  const clearMessages = useCallback(() => {
    setMessages([]);
    setCurrentUserInput('');
    setError(null);
    storage.current.clearCurrentSession();
    console.log('🗑️ Historial de mensajes limpiado');
  }, []);

  // Control inteligente del micrófono
  const enableSmartMicrophone = useCallback(() => {
    speechService.current.enableSmartMicrophoneControl();
  }, []);

  const disableSmartMicrophone = useCallback(() => {
    speechService.current.disableSmartMicrophoneControl();
  }, []);

  // Auto-iniciar conversación cuando el juego comienza
  useEffect(() => {
    if (isGameStarted && !isActive && isSupported) {
      const timer = setTimeout(async () => {
        const success = await startConversation();
        if (success) {
          enableSmartMicrophone(); // 👈 Esto activa el control inteligente
        }
      }, 800);

      return () => clearTimeout(timer);
    }
  }, [isGameStarted, isActive, isSupported, startConversation, enableSmartMicrophone]);

  // Cleanup al desmontar
  useEffect(() => {
    return () => {
      if (isActive) {
        stopConversation();
      }
    };
  }, [isActive, stopConversation]);

  return {
    // Estado
    isActive,
    conversationState,
    messages,
    currentUserInput,
    isSupported,
    error,

    // Controles
    startConversation,
    stopConversation,
    sendMessage,
    clearMessages,
    addInitialMessage, // 👈 Nuevo método expuesto

    // Configuración
    enableSmartMicrophone,
    disableSmartMicrophone,
  };
};
